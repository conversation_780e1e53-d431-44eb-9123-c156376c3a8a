{"name": "chewyai-backend", "version": "1.0.0", "type": "module", "license": "MIT", "description": "ChewyAI Backend API Server", "scripts": {"dev": "tsx index.ts", "build": "esbuild index.ts --bundle --outfile=dist/index.cjs --platform=node --format=cjs --external:pg-native --external:node-fetch", "start": "node dist/index.cjs", "check": "tsc --noEmit", "db:push": "drizzle-kit push"}, "dependencies": {"@hono/node-server": "^1.14.1", "@neondatabase/serverless": "^0.10.4", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.0", "express": "^4.21.2", "express-session": "^1.18.1", "express-validator": "^7.2.1", "hono": "^4.7.9", "memorystore": "^1.6.7", "passport": "^0.7.0", "passport-local": "^1.0.0", "postgres": "^3.4.5", "uuid": "^11.1.0", "whatwg-url": "^14.2.0", "ws": "^8.18.0", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.17", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/pg": "^8.15.2", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.13", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "tsx": "^4.19.1", "typescript": "5.6.3"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}