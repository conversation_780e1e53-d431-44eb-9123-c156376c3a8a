@echo off
REM Simple batch script to start ChewyAI development servers
REM Compatible with Windows Command Prompt and PowerShell

echo [DEV] Starting ChewyAI development environment...

REM Check if .env file exists, create from example if not
if not exist ".env" (
    if exist "env.example" (
        echo [WARNING] .env file not found, creating from env.example...
        copy "env.example" ".env" >nul
        echo [SUCCESS] Created .env file from env.example
        echo [WARNING] Please review and update .env file with your specific configuration if needed
    ) else (
        echo [ERROR] No .env or env.example file found!
        pause
        exit /b 1
    )
)

REM Set default environment variables
set NODE_ENV=development
set FRONTEND_PORT=3000
set BACKEND_PORT=5000
set PORT=5000

echo [DEV] Environment configuration:
echo   - NODE_ENV: %NODE_ENV%
echo   - Frontend Port: %FRONTEND_PORT%
echo   - Backend Port: %BACKEND_PORT%
echo   - Frontend URL: http://localhost:%FRONTEND_PORT%
echo   - Backend API URL: http://localhost:%BACKEND_PORT%

REM Check system requirements
echo [DEV] Checking system requirements...

node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed or not in PATH
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo [SUCCESS] Node.js %NODE_VERSION% and npm %NPM_VERSION% are available

REM Check dependencies
if not exist "node_modules" (
    echo [WARNING] Root node_modules not found, installing dependencies...
    npm install
)

if not exist "frontend\node_modules" (
    echo [WARNING] Frontend node_modules not found, installing dependencies...
    cd frontend
    npm install
    cd ..
)

if not exist "backend\node_modules" (
    echo [WARNING] Backend node_modules not found, installing dependencies...
    cd backend
    npm install
    cd ..
)

echo.
echo [DEV] Starting development servers...
echo   - Frontend (React + Vite): http://localhost:%FRONTEND_PORT%
echo   - Backend (Express + tsx): http://localhost:%BACKEND_PORT%
echo.
echo [WARNING] Press Ctrl+C to stop all servers
echo.

REM Start development servers
echo [DEV] Executing: npm run dev
npm run dev

if errorlevel 1 (
    echo [ERROR] Failed to start development servers
    echo [DEV] Check the error messages above for troubleshooting information
    pause
    exit /b 1
)

echo [SUCCESS] Development servers started successfully
