# PowerShell development startup script for ChewyAI
# Alternative to dev.sh for Windows environments
# Run with: powershell -ExecutionPolicy Bypass -File dev.ps1

param(
    [switch]$Debug = $false
)

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green" 
    Yellow = "Yellow"
    Blue = "Blue"
    White = "White"
}

function Write-Status {
    param([string]$Message)
    Write-Host "[DEV] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

function Write-Debug {
    param([string]$Message)
    if ($Debug) {
        Write-Host "[DEBUG] $Message" -ForegroundColor $Colors.Blue
    }
}

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

Write-Status "Starting ChewyAI development environment..."

# Check if .env file exists, create from example if not
if (-not (Test-Path ".env")) {
    if (Test-Path "env.example") {
        Write-Warning ".env file not found, creating from env.example..."
        Copy-Item "env.example" ".env"
        Write-Success "Created .env file from env.example"
        Write-Warning "Please review and update .env file with your specific configuration if needed"
    } else {
        Write-Error "No .env or env.example file found!"
        exit 1
    }
}

# Load environment variables from .env file
if (Test-Path ".env") {
    Write-Status "Loading environment variables from .env..."
    try {
        Get-Content ".env" | ForEach-Object {
            if ($_ -match "^\s*([^#][^=]*)\s*=\s*(.*)\s*$") {
                $key = $matches[1].Trim()
                $value = $matches[2].Trim()
                
                # Remove quotes if present
                $value = $value -replace '^["'']|["'']$', ''
                
                # Set environment variable
                [Environment]::SetEnvironmentVariable($key, $value, "Process")
                Write-Debug "Set $key = $value"
            }
        }
        Write-Success "Environment variables loaded successfully"
    } catch {
        Write-Warning "Failed to load .env file: $($_.Exception.Message)"
    }
}

# Set development environment variables with fallbacks
$env:NODE_ENV = if ($env:NODE_ENV) { $env:NODE_ENV } else { "development" }
$env:FRONTEND_PORT = if ($env:FRONTEND_PORT) { $env:FRONTEND_PORT } else { "3000" }
$env:BACKEND_PORT = if ($env:BACKEND_PORT) { $env:BACKEND_PORT } else { "5000" }
$env:PORT = if ($env:PORT) { $env:PORT } else { $env:BACKEND_PORT }

Write-Status "Environment configuration:"
Write-Host "  - NODE_ENV: $env:NODE_ENV"
Write-Host "  - Frontend Port: $env:FRONTEND_PORT"
Write-Host "  - Backend Port: $env:BACKEND_PORT"
Write-Host "  - Frontend URL: http://localhost:$env:FRONTEND_PORT"
Write-Host "  - Backend API URL: http://localhost:$env:BACKEND_PORT"

# Check system requirements
Write-Status "Checking system requirements..."

try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Success "Node.js $nodeVersion and npm $npmVersion are available"
} catch {
    Write-Error "Node.js or npm is not installed or not in PATH"
    exit 1
}

# Check dependencies
if (-not (Test-Path "node_modules")) {
    Write-Warning "Root node_modules not found, installing dependencies..."
    npm install
}

if (-not (Test-Path "frontend/node_modules")) {
    Write-Warning "Frontend node_modules not found, installing dependencies..."
    Set-Location "frontend"
    npm install
    Set-Location ".."
}

if (-not (Test-Path "backend/node_modules")) {
    Write-Warning "Backend node_modules not found, installing dependencies..."
    Set-Location "backend"
    npm install
    Set-Location ".."
}

Write-Host ""
Write-Status "Starting development servers..."
Write-Host "  - Frontend (React + Vite): http://localhost:$env:FRONTEND_PORT"
Write-Host "  - Backend (Express + tsx): http://localhost:$env:BACKEND_PORT"
Write-Host ""
Write-Warning "Press Ctrl+C to stop all servers"
Write-Host ""

# Start development servers
Write-Status "Executing: npm run dev"

try {
    npm run dev
    Write-Success "Development servers started successfully"
} catch {
    Write-Error "Failed to start development servers: $($_.Exception.Message)"
    Write-Status "Check the error messages above for troubleshooting information"
    exit 1
}
