/// <reference types="vite/client" />
import { createClient } from "@supabase/supabase-js";
import { Database } from "../types/supabase"; // Assuming you will generate this type

// Pull credentials strictly from environment – no hard-coded fallbacks
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl) {
  throw new Error("VITE_SUPABASE_URL is not set and no fallback available");
}

if (!supabaseAnonKey) {
  throw new Error(
    "VITE_SUPABASE_ANON_KEY is not set and no fallback available"
  );
}

console.log("✓ Supabase client initialized");
console.log("✓ URL:", supabaseUrl);
console.log("✓ Using anon key:", supabaseAnonKey ? "Configured" : "Missing");

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);
