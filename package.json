{"name": "chewyai", "version": "1.0.0", "type": "module", "license": "MIT", "description": "ChewyAI - AI-Powered Study Tool", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "cd frontend && npm run start", "start:backend": "cd backend && npm run start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules frontend/dist backend/dist", "check": "concurrently \"npm run check:frontend\" \"npm run check:backend\"", "check:frontend": "cd frontend && npm run check", "check:backend": "cd backend && npm run check"}, "workspaces": ["frontend", "backend"], "devDependencies": {"concurrently": "^9.1.2"}, "dependencies": {"@supabase/supabase-js": "^2.50.0"}}